generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgres"
  url      = env("DATABASE_URL")
}

// Basic enums
enum RepaymentType {
  REGULAR
  INTEREST_ONLY
  PARTIAL
}

model User {
  id            Int            @id @default(autoincrement())
  name          String
  email         String         @unique
  password      String
  role          String         @default("user")
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  chitFunds     ChitFund[]
  globalMembers GlobalMember[]
  loans         Loan[]
  partners      Partner[]
  repayments    Repayment[]
  transactions  Transaction[]
}

model ChitFund {
  id                     Int                @id @default(autoincrement())
  name                   String
  totalAmount            Float
  monthlyContribution    Float
  firstMonthContribution Float?             // For Fixed type chit funds
  duration               Int
  membersCount           Int
  status                 String             @default("Active")
  startDate              DateTime
  currentMonth           Int                @default(1)
  nextAuctionDate        DateTime?
  description            String?
  chitFundType           String             @default("Auction") // "Auction" or "Fixed"
  createdAt              DateTime           @default(now())
  updatedAt              DateTime           @updatedAt
  createdById            Int
  auctions            Auction[]
  createdBy           User               @relation(fields: [createdById], references: [id], map: "ChitFund_createdById_fkey")
  contributions       Contribution[]
  members             Member[]
  fixedAmounts        ChitFundFixedAmount[]

  @@index([createdById], map: "ChitFund_createdById_idx")
}

model GlobalMember {
  id              Int      @id @default(autoincrement())
  name            String
  contact         String
  email           String?
  address         String?
  notes           String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  createdById     Int
  createdBy       User     @relation(fields: [createdById], references: [id], map: "GlobalMember_createdById_fkey")
  loans           Loan[]   @relation("BorrowerToLoan")
  chitFundMembers Member[]

  @@index([createdById], map: "GlobalMember_createdById_idx")
}

model Member {
  id             Int            @id @default(autoincrement())
  globalMemberId Int
  chitFundId     Int
  joinDate       DateTime       @default(now())
  auctionWon     Boolean        @default(false)
  auctionMonth   Int?
  contribution   Float
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  auctions       Auction[]
  contributions  Contribution[]
  chitFund       ChitFund       @relation(fields: [chitFundId], references: [id], map: "Member_chitFundId_fkey")
  globalMember   GlobalMember   @relation(fields: [globalMemberId], references: [id], map: "Member_globalMemberId_fkey")

  @@index([chitFundId], map: "Member_chitFundId_idx")
  @@index([globalMemberId], map: "Member_globalMemberId_idx")
}

model Contribution {
  id                       Int       @id @default(autoincrement())
  amount                   Float
  month                    Int
  paidDate                 DateTime
  memberId                 Int
  chitFundId               Int
  balance                  Float     @default(0)
  balancePaymentDate       DateTime?
  balancePaymentStatus     String?   @default("Pending")
  actualBalancePaymentDate DateTime?
  notes                    String?
  createdAt                DateTime  @default(now())
  updatedAt                DateTime  @updatedAt
  chitFund                 ChitFund  @relation(fields: [chitFundId], references: [id], map: "Contribution_chitFundId_fkey")
  member                   Member    @relation(fields: [memberId], references: [id], map: "Contribution_memberId_fkey")

  @@index([chitFundId], map: "Contribution_chitFundId_idx")
  @@index([memberId], map: "Contribution_memberId_idx")
}

model Auction {
  id              Int      @id @default(autoincrement())
  chitFundId      Int
  month           Int
  date            DateTime
  winnerId        Int
  amount          Float
  lowestBid       Float?
  highestBid      Float?
  numberOfBidders Int?
  notes           String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  chitFund        ChitFund @relation(fields: [chitFundId], references: [id], map: "Auction_chitFundId_fkey")
  winner          Member   @relation(fields: [winnerId], references: [id], map: "Auction_winnerId_fkey")

  @@index([chitFundId], map: "Auction_chitFundId_idx")
  @@index([winnerId], map: "Auction_winnerId_idx")
}

model ChitFundFixedAmount {
  id         Int      @id @default(autoincrement())
  chitFundId Int
  month      Int
  amount     Float
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  chitFund   ChitFund @relation(fields: [chitFundId], references: [id], map: "ChitFundFixedAmount_chitFundId_fkey")

  @@unique([chitFundId, month])
  @@index([chitFundId], map: "ChitFundFixedAmount_chitFundId_idx")
}

model Loan {
  id                Int               @id @default(autoincrement())
  borrowerId        Int
  loanType          String
  amount            Float
  interestRate      Float
  documentCharge    Float             @default(0)
  currentMonth      Int               @default(0)
  installmentAmount Float             @default(0)
  duration          Int
  disbursementDate  DateTime
  repaymentType     String
  remainingAmount   Float
  overdueAmount     Float             @default(0)
  missedPayments    Int               @default(0)
  nextPaymentDate   DateTime?
  status            String            @default("Active")
  purpose           String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  createdById       Int
  borrower          GlobalMember      @relation("BorrowerToLoan", fields: [borrowerId], references: [id], map: "Loan_borrowerId_fkey")
  createdBy         User              @relation(fields: [createdById], references: [id], map: "Loan_createdById_fkey")
  paymentSchedules  PaymentSchedule[]
  repayments        Repayment[]

  @@index([borrowerId], map: "Loan_borrowerId_idx")
  @@index([createdById], map: "Loan_createdById_idx")
}

model Repayment {
  id             Int       @id @default(autoincrement())
  amount         Float
  // We store both the logged date and actual paid date
  date          DateTime            // When the repayment was recorded in system
  paidDate      DateTime            // When the payment was actually received
  period        Int                 // Payment period number (e.g., 1st installment, 2nd installment)
  paymentType   RepaymentType       // Regular, Interest, Partial
  loanId        Int
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  createdById   Int
  collected_by_id Int
  entered_by_id   Int
  loan          Loan     @relation(fields: [loanId], references: [id], onDelete: Cascade)
  createdBy     User     @relation(fields: [createdById], references: [id])
  collectedBy   Partner  @relation("CollectedRepayments", fields: [collected_by_id], references: [id])
  enteredBy     Partner  @relation("EnteredRepayments", fields: [entered_by_id], references: [id])

  @@index([createdById])
  @@index([loanId])
  @@index([collected_by_id])
  @@index([entered_by_id])
  @@index([paidDate])
}

model PaymentSchedule {
  id                Int       @id @default(autoincrement())
  loanId            Int
  period            Int
  dueDate           DateTime
  amount            Float
  status            String    @default("Pending")
  actualPaymentDate DateTime?
  notes             String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  loan              Loan      @relation(fields: [loanId], references: [id], map: "PaymentSchedule_loanId_fkey")

  @@index([loanId], map: "PaymentSchedule_loanId_idx")
}

model EmailLog {
  id          Int      @id @default(autoincrement())
  emailType   String   // "monthly" or "weekly"
  period      String   // "2024-01" for monthly, "2024-W01" for weekly
  sentDate    DateTime
  status      String   @default("sent") // "sent", "failed", "recovered"
  recipients  String   // JSON array of recipient emails
  fileName    String?  // Name of the attached file
  isRecovery  Boolean  @default(false) // True if this was a recovery email
  errorMessage String? // Error message if failed
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([emailType, period])
  @@index([emailType, sentDate])
}

model Partner {
  id                 Int           @id @default(autoincrement())
  name               String
  code               String?       // Unique code per user (e.g., SELF, AGENT)
  isActive           Boolean       @default(true)
  createdAt          DateTime      @default(now())
  updatedAt          DateTime      @updatedAt
  createdById        Int
  createdBy          User         @relation(fields: [createdById], references: [id])
  fromTransactions   Transaction[] @relation("FromPartnerTransactions")
  toTransactions     Transaction[] @relation("ToPartnerTransactions")
  collectedRepayments Repayment[]  @relation("CollectedRepayments")
  enteredRepayments  Repayment[]  @relation("EnteredRepayments")

  @@index([createdById])
  @@unique([createdById, code], name: "Partner_createdById_code_key", map: "Partner_createdById_code_key")
}

model Transaction {
  id            Int       @id @default(autoincrement())
  amount        Float
  description   String?
  date          DateTime
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  createdById   Int
  createdBy     User     @relation(fields: [createdById], references: [id])
  from_partner_id Int?
  to_partner_id   Int?
  fromPartner    Partner? @relation("FromPartnerTransactions", fields: [from_partner_id], references: [id])
  toPartner      Partner? @relation("ToPartnerTransactions", fields: [to_partner_id], references: [id])

  @@index([createdById])
  @@index([from_partner_id])
  @@index([to_partner_id])
}
